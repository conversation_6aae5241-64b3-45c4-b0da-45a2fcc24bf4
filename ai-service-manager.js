// AI服务管理模块
class AIServiceManager {
  constructor() {
    this.providers = this.initializeProviders();
    this.defaultTimeout = 30000; // 30秒超时
    this.logLevel = this.getLogLevel();
  }

  // 获取日志级别配置
  getLogLevel() {
    // 在生产环境中可以通过环境变量或配置文件控制
    // 开发环境: 'debug', 生产环境: 'error'
    // 临时设置为debug级别以便调试问题
    return 'debug';
  }

  // 安全的日志记录方法
  safeLog(level, message, data = {}) {
    const levels = { debug: 0, info: 1, warn: 2, error: 3 };
    const currentLevel = levels[this.logLevel] || 1;
    const messageLevel = levels[level] || 1;

    if (messageLevel >= currentLevel) {
      const logMethod = console[level] || console.log;
      logMethod(message, data);
    }
  }

  // 初始化AI服务提供商配置
  initializeProviders() {
    // 尝试从环境配置文件获取配置
    if (typeof EnvConfig !== 'undefined') {
      try {
        const envConfig = EnvConfig.getCurrentConfig();
        const envProviders = envConfig.aiProviders;

        this.safeLog('debug', '🔍 环境配置详情', {
          hasEnvConfig: !!envConfig,
          hasAiProviders: !!envProviders,
          providersKeys: envProviders ? Object.keys(envProviders) : []
        });

        // 转换环境配置格式为内部格式
        const providers = {};
        for (const [id, provider] of Object.entries(envProviders)) {
          providers[id] = {
            ...provider,
            headers: (apiKey) => {
              const baseHeaders = {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
              };

              // 合并供应商特定的头部
              if (provider.headers) {
                return { ...baseHeaders, ...provider.headers };
              }

              return baseHeaders;
            }
          };

          this.safeLog('debug', `🔧 配置供应商: ${id}`, {
            name: provider.name,
            baseUrl: provider.baseUrl,
            defaultModel: provider.defaultModel,
            modelsCount: provider.models ? Object.keys(provider.models).length : 0
          });
        }

        this.safeLog('info', '✅ 从环境配置加载AI供应商配置', {
          providersCount: Object.keys(providers).length,
          providerIds: Object.keys(providers)
        });

        return providers;
      } catch (error) {
        this.safeLog('error', '❌ 加载环境配置失败，使用默认配置', {
          error: error.message,
          stack: error.stack
        });
      }
    } else {
      this.safeLog('warn', '⚠️ EnvConfig 未定义，使用内置配置');
    }

    // 备用配置
    this.safeLog('warn', '⚠️ 环境配置不可用，使用内置配置');
    return {
      siliconflow: {
        name: '硅基流动',
        baseUrl: 'https://api.siliconflow.cn/v1',
        models: {
          'Qwen/Qwen3-8B': 'Qwen3-8B (免费)',
          'Qwen/Qwen2.5-14B-Instruct': 'Qwen2.5-14B-Instruct',
          'Qwen/Qwen2.5-32B-Instruct': 'Qwen2.5-32B-Instruct',
          'Qwen/Qwen2.5-72B-Instruct': 'Qwen2.5-72B-Instruct',
          'THUDM/glm-4-9b-chat': 'GLM-4-9B-Chat (免费)',
          'internlm/internlm2_5-7b-chat': 'InternLM2.5-7B-Chat (免费)',
          'deepseek-ai/DeepSeek-V2.5': 'DeepSeek-V2.5',
          'meta-llama/Meta-Llama-3.1-8B-Instruct': 'Llama-3.1-8B-Instruct (免费)'
        },
        defaultModel: 'Qwen/Qwen3-8B',
        headers: (apiKey) => ({
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        })
      },
      openrouter: {
        name: 'OpenRouter',
        baseUrl: 'https://openrouter.ai/api/v1',
        models: {
          'anthropic/claude-3-haiku': 'Claude 3 Haiku',
          'anthropic/claude-3-sonnet': 'Claude 3 Sonnet',
          'openai/gpt-4o-mini': 'GPT-4o Mini',
          'openai/gpt-4o': 'GPT-4o',
          'meta-llama/llama-3.1-8b-instruct': 'Llama 3.1 8B',
          'google/gemini-pro': 'Gemini Pro'
        },
        defaultModel: 'anthropic/claude-3-haiku',
        headers: (apiKey) => {
          const headers = {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          };

          // 安全地添加扩展相关的头部
          if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getURL) {
            headers['HTTP-Referer'] = chrome.runtime.getURL('');
            headers['X-Title'] = 'Save to Flomo Extension';
          }

          return headers;
        }
      },
      deepseek: {
        name: 'DeepSeek',
        baseUrl: 'https://api.deepseek.com/v1',
        models: {
          'deepseek-chat': 'DeepSeek Chat',
          'deepseek-coder': 'DeepSeek Coder'
        },
        defaultModel: 'deepseek-chat',
        headers: (apiKey) => ({
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        })
      },
      moonshot: {
        name: 'Moonshot AI',
        baseUrl: 'https://api.moonshot.cn/v1',
        models: {
          'moonshot-v1-8k': 'Moonshot v1 8K',
          'moonshot-v1-32k': 'Moonshot v1 32K',
          'moonshot-v1-128k': 'Moonshot v1 128K'
        },
        defaultModel: 'moonshot-v1-8k',
        headers: (apiKey) => ({
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        })
      }
    };
  }

  // 获取所有可用的服务提供商
  getProviders() {
    return Object.keys(this.providers).map(key => ({
      id: key,
      name: this.providers[key].name,
      models: this.providers[key].models,
      defaultModel: this.providers[key].defaultModel
    }));
  }

  // 获取指定提供商的模型列表
  getModels(providerId) {
    const provider = this.providers[providerId];
    return provider ? provider.models : {};
  }

  // 发送AI请求 (支持流式和非流式)
  async sendRequest(providerId, apiKey, model, messages, options = {}) {
    // 检查是否启用流式输出
    const isStreaming = options.stream === true;

    // 安全的日志记录，不暴露敏感信息
    this.safeLog('info', '🚀 AI请求开始', {
      providerId,
      model,
      messageCount: messages ? messages.length : 0,
      hasApiKey: !!apiKey,
      isStreaming,
      requestOptions: {
        temperature: options.temperature || 0.7,
        maxTokens: options.maxTokens || 2000
      }
    });

    const provider = this.providers[providerId];
    if (!provider) {
      this.safeLog('error', '❌ 不支持的AI服务提供商:', { providerId });
      throw new Error(`不支持的AI服务提供商: ${providerId}`);
    }

    if (!apiKey) {
      this.safeLog('error', '❌ API密钥未配置:', { providerId });
      throw new Error(`请配置 ${provider.name} 的API密钥`);
    }

    const finalModel = model || provider.defaultModel;

    // 详细的调试日志
    this.safeLog('debug', '🔍 模型配置详情', {
      providerId,
      inputModel: model,
      providerDefaultModel: provider.defaultModel,
      finalModel: finalModel,
      hasModel: !!finalModel
    });

    // 如果最终模型为空，抛出错误
    if (!finalModel) {
      const errorMsg = `模型配置错误: providerId=${providerId}, inputModel=${model}, providerDefaultModel=${provider.defaultModel}`;
      this.safeLog('error', '❌ 模型配置为空', { providerId, model, providerDefaultModel: provider.defaultModel });
      throw new Error(errorMsg);
    }

    const requestBody = {
      model: finalModel,
      messages: messages,
      temperature: options.temperature || 0.7,
      max_tokens: options.maxTokens || 2000,
      stream: isStreaming
    };

    // 特殊处理不同提供商的参数
    if (providerId === 'openrouter') {
      requestBody.route = 'fallback';
    }

    const requestUrl = `${provider.baseUrl}/chat/completions`;
    const requestHeaders = provider.headers(apiKey);

    this.safeLog('debug', '📤 发送请求', {
      url: requestUrl,
      method: 'POST',
      hasAuthorization: !!requestHeaders.Authorization,
      contentType: requestHeaders['Content-Type'],
      bodySize: JSON.stringify(requestBody).length,
      isStreaming
    });

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.defaultTimeout);

    // 根据是否流式选择不同的处理方式
    if (isStreaming) {
      return this._handleStreamingRequest(requestUrl, requestHeaders, requestBody, controller, timeoutId, options);
    } else {
      return this._handleNormalRequest(requestUrl, requestHeaders, requestBody, controller, timeoutId);
    }
  }

  // 处理流式请求
  async _handleStreamingRequest(requestUrl, requestHeaders, requestBody, controller, timeoutId, options) {
    try {
      const response = await fetch(requestUrl, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      this.safeLog('debug', '📥 收到流式响应', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        contentType: response.headers.get('content-type')
      });

      if (!response.ok) {
        const errorText = await response.text();
        this.safeLog('error', '❌ 流式请求失败:', {
          status: response.status,
          statusText: response.statusText,
          errorText: errorText.substring(0, 200)
        });
        throw new Error(this._getFriendlyErrorMessage(response.status, errorText));
      }

      if (!response.body) {
        throw new Error('响应体为空，无法建立流式连接');
      }

      // 处理流式数据
      return this._processStreamingResponse(response.body, options);

    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        this.safeLog('warn', '⏰ 流式请求超时');
        throw new Error('请求超时，请检查网络连接');
      }

      this.safeLog('error', '❌ 流式请求异常:', { error: error.message });
      throw error;
    }
  }

  // 处理流式响应数据
  async _processStreamingResponse(responseBody, options) {
    const reader = responseBody.pipeThrough(new TextDecoderStream()).getReader();
    const onChunk = options.onChunk || (() => { });
    const onComplete = options.onComplete || (() => { });
    const onError = options.onError || (() => { });

    let fullContent = '';
    let usage = null;
    let model = null;

    try {
      while (true) {
        const { value, done } = await reader.read();

        if (done) {
          this.safeLog('info', '✅ 流式响应完成', {
            contentLength: fullContent.length,
            model: model
          });

          const result = {
            content: fullContent,
            usage: usage,
            model: model,
            success: true
          };

          onComplete(result);
          return result;
        }

        if (value) {
          // 解析SSE数据
          const lines = value.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim();

              if (data === '[DONE]') {
                continue;
              }

              try {
                const parsed = JSON.parse(data);

                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta) {
                  const delta = parsed.choices[0].delta;

                  if (delta.content) {
                    fullContent += delta.content;
                    onChunk(delta.content, fullContent);
                  }
                }

                // 保存模型和使用信息
                if (parsed.model) {
                  model = parsed.model;
                }
                if (parsed.usage) {
                  usage = parsed.usage;
                }

              } catch (parseError) {
                this.safeLog('warn', '⚠️ 解析流式数据失败:', { data: data.substring(0, 100) });
              }
            }
          }
        }
      }
    } catch (error) {
      this.safeLog('error', '❌ 流式数据处理失败:', { error: error.message });
      onError(error);
      throw error;
    } finally {
      reader.releaseLock();
    }
  }

  // 处理普通请求 (原有逻辑)
  async _handleNormalRequest(requestUrl, requestHeaders, requestBody, controller, timeoutId) {

    try {
      const response = await fetch(requestUrl, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      this.safeLog('debug', '📥 收到响应', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        contentType: response.headers.get('content-type')
      });

      if (!response.ok) {
        let errorData;
        let responseText;
        try {
          responseText = await response.text();
          this.safeLog('debug', '📄 错误响应内容长度:', { length: responseText.length });
          errorData = JSON.parse(responseText);
        } catch (parseError) {
          this.safeLog('warn', '⚠️ 无法解析错误响应为JSON:', { error: parseError.message });
          errorData = { error: { message: responseText || '未知错误' } };
        }

        const errorMessage = errorData.error?.message || errorData.message || `HTTP ${response.status}: ${response.statusText}`;
        this.safeLog('error', '❌ API请求失败:', {
          status: response.status,
          statusText: response.statusText,
          errorMessage,
          hasErrorData: !!errorData
        });

        throw new Error(errorMessage);
      }

      const responseText = await response.text();
      this.safeLog('debug', '📄 成功响应内容长度:', { length: responseText.length });

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        this.safeLog('error', '❌ 无法解析响应JSON:', { error: parseError.message });
        throw new Error('AI服务返回的数据格式不正确');
      }

      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        this.safeLog('error', '❌ 响应数据结构不正确:', { hasChoices: !!data.choices });
        throw new Error('AI服务返回的数据格式不正确');
      }

      const result = {
        content: data.choices[0].message.content,
        usage: data.usage,
        model: data.model
      };

      this.safeLog('info', '✅ AI请求成功', {
        contentLength: result.content.length,
        model: result.model,
        hasUsage: !!result.usage
      });
      return result;

    } catch (error) {
      clearTimeout(timeoutId);

      this.safeLog('error', '❌ AI请求异常:', {
        name: error.name,
        message: error.message
      });

      // 提供更友好的错误提示
      const friendlyError = this._getFriendlyErrorMessage(error);
      throw new Error(friendlyError);
    }
  }

  // 获取友好的错误提示信息
  _getFriendlyErrorMessage(error) {
    const message = (error?.message || '').toLowerCase();

    // 网络相关错误
    if (error.name === 'AbortError') {
      return '请求超时，请稍后重试或检查网络连接';
    }

    if (message.includes('failed to fetch') || message.includes('network error')) {
      return '网络连接失败，请检查您的网络连接或防火墙设置';
    }

    if (message.includes('cors')) {
      return '跨域请求被阻止，请检查API配置或联系技术支持';
    }

    // API 相关错误
    if (message.includes('unauthorized') || message.includes('401')) {
      return 'API密钥无效或已过期，请检查您的API密钥配置';
    }

    if (message.includes('forbidden') || message.includes('403')) {
      return 'API访问被拒绝，请检查您的API密钥权限';
    }

    if (message.includes('not found') || message.includes('404')) {
      return 'API地址不正确，请检查服务商配置';
    }

    if (message.includes('rate limit') || message.includes('429')) {
      return 'API调用频率超限，请稍后重试';
    }

    if (message.includes('quota') || message.includes('billing')) {
      return 'API配额不足或账户余额不足，请检查您的账户状态';
    }

    if (message.includes('model') && message.includes('not found')) {
      return '指定的AI模型不存在，请选择其他模型';
    }

    if (message.includes('server error') || message.includes('500')) {
      return 'AI服务暂时不可用，请稍后重试';
    }

    if (message.includes('bad gateway') || message.includes('502')) {
      return 'AI服务网关错误，请稍后重试';
    }

    if (message.includes('service unavailable') || message.includes('503')) {
      return 'AI服务暂时维护中，请稍后重试';
    }

    // 默认错误信息
    return error?.message || '未知错误，请稍后重试';
  }

  // 验证API密钥
  async validateApiKey(providerId, apiKey, model) {
    // 为API密钥验证创建缓存键（不包含完整密钥）
    const cacheKey = `${providerId}:${model}:${apiKey.substring(0, 8)}...`;

    // 检查缓存
    if (typeof requestCache !== 'undefined') {
      const cached = await requestCache.get('validateApiKey', cacheKey, { providerId, model });
      if (cached) {
        console.log('🎯 使用缓存的API密钥验证结果');
        return cached;
      }
    }

    try {
      const testMessages = [
        {
          role: 'user',
          content: '请回复"测试成功"'
        }
      ];

      const result = await this.sendRequest(providerId, apiKey, model, testMessages, {
        maxTokens: 10,
        temperature: 0
      });

      const finalResult = {
        valid: true,
        message: 'API密钥验证成功',
        model: result.model
      };

      // 缓存验证结果（较长的缓存时间）
      if (typeof requestCache !== 'undefined') {
        await requestCache.set('validateApiKey', cacheKey, { providerId, model }, finalResult);
      }

      return finalResult;
    } catch (error) {
      const errorResult = {
        valid: false,
        message: error?.message || '验证失败'
      };

      // 不缓存失败结果，因为可能是临时网络问题
      return errorResult;
    }
  }

  // 获取服务提供商状态
  async getProviderStatus(providerId) {
    const provider = this.providers[providerId];
    if (!provider) {
      return { available: false, message: '不支持的服务提供商' };
    }

    try {
      // 简单的健康检查
      const response = await fetch(provider.baseUrl, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });

      return {
        available: response.status < 500,
        message: response.status < 500 ? '服务正常' : '服务暂时不可用'
      };
    } catch (error) {
      return {
        available: false,
        message: '无法连接到服务'
      };
    }
  }

  // 估算token使用量
  estimateTokens(text) {
    // 简单的token估算（中文字符约等于1.5个token，英文单词约等于1.3个token）
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    const otherChars = text.length - chineseChars - englishWords;

    return Math.ceil(chineseChars * 1.5 + englishWords * 1.3 + otherChars * 0.5);
  }

  // 获取推荐的模型配置
  getRecommendedModel(task, contentLength) {
    const tokens = this.estimateTokens(contentLength.toString());

    // 根据任务类型和内容长度推荐模型
    if (task === 'summary' && tokens > 4000) {
      return {
        provider: 'moonshot',
        model: 'moonshot-v1-32k',
        reason: '长文本摘要推荐使用大上下文模型'
      };
    } else if (task === 'tags' || task === 'optimize') {
      return {
        provider: 'siliconflow',
        model: 'Qwen/Qwen3-8B',
        reason: '标签生成和内容优化推荐使用高效模型'
      };
    } else if (task === 'translate') {
      return {
        provider: 'openrouter',
        model: 'anthropic/claude-3-haiku',
        reason: '语言优化推荐使用Claude模型'
      };
    }

    // 默认推荐
    return {
      provider: 'siliconflow',
      model: 'Qwen/Qwen3-8B',
      reason: '通用任务推荐'
    };
  }
}

// 导出服务管理器实例
const aiServiceManager = new AIServiceManager();

// 导出到全局作用域
if (typeof window !== 'undefined') {
  window.aiServiceManager = aiServiceManager;
  window.AIServiceManager = AIServiceManager;
} else if (typeof self !== 'undefined') {
  // Service Worker环境
  self.aiServiceManager = aiServiceManager;
  self.AIServiceManager = AIServiceManager;
}

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AIServiceManager;
}
