# 🧹 项目清理指南

## 📁 需要保留的核心文件

### 必要文件（6个核心文件）
```
chrome-plugin-save-to-flomo/
├── manifest.json          # 插件配置文件
├── background.js          # 后台脚本
├── content.js            # 内容脚本
├── popup.html            # 弹窗页面
├── popup.js              # 弹窗脚本
├── sidepanel.html        # 侧边栏页面
├── sidepanel.js          # 侧边栏脚本
├── sidepanel.css         # 样式文件
└── icon.png              # 插件图标
```

### 测试和文档文件
```
├── test-plugin.html      # 测试页面
├── TESTING_GUIDE.md      # 测试指南
├── cleanup-old-files.md  # 清理指南
└── README.md             # 项目说明
```

## 🗑️ 可以删除的文件和目录

### 复杂的模块化文件
```
src/                      # 整个src目录都可以删除
├── modules/
│   ├── sidepanel/
│   │   ├── sidepanel.js
│   │   ├── ui-renderer.js
│   │   ├── event-handler.js
│   │   ├── ai-service.js
│   │   └── data-manager.js
│   └── ...
├── components/
├── services/
├── utils/
└── ...
```

### 过度工程化的功能文件
```
├── ai-functions-optimized.js
├── ai-functions.js
├── ai-service-manager.js
├── content-onboarding.js
├── dom-optimizer.js
├── error-config.js
├── error-handler-sw.js
├── error-handler.js
├── html-to-markdown.js
├── input-validator-sw.js
├── input-validator.js
├── notification-manager-sw.js
├── notification-manager.js
├── onboarding-debug.js
├── onboarding.js
├── performance-config.js
├── performance-optimizer.js
├── request-cache.js
├── secure-storage.js
├── security-test.html
├── security-test.js
├── service-worker-validation.js
└── ...
```

### 测试和调试文件
```
├── tests/                # 测试目录
├── docs/                 # 文档目录（部分可保留）
├── simple-test.html
├── test-ai-functions.html
├── test-config.js
├── test-coverage.js
├── test-framework.js
├── popup-preview.html
├── performance-monitor.html
└── ...
```

### 配置文件
```
├── env.config.example.js
├── env.config.js         # 如果不需要环境配置
└── options.html          # 如果不需要选项页面
└── options.js
```

## 🔧 清理步骤

### 1. 备份重要文件
在删除之前，确保已经备份了以下重要文件：
- 当前的 `manifest.json`
- 重构后的 `background.js`
- 简化的 `popup.html` 和 `popup.js`
- 新的 `sidepanel.html` 和 `sidepanel.js`
- 样式文件 `sidepanel.css`

### 2. 删除不需要的目录
```bash
# 删除整个src目录
rm -rf src/

# 删除测试目录
rm -rf tests/

# 删除部分文档目录（保留重要文档）
rm -rf docs/
```

### 3. 删除单个文件
```bash
# 删除AI相关的复杂文件
rm ai-functions*.js
rm ai-service-manager.js

# 删除错误处理相关文件
rm error-*.js
rm *-sw.js

# 删除性能监控文件
rm performance-*.js
rm performance-monitor.html

# 删除安全相关文件
rm secure-storage.js
rm security-test.*

# 删除其他不需要的文件
rm content-onboarding.js
rm dom-optimizer.js
rm html-to-markdown.js
rm input-validator*.js
rm notification-manager*.js
rm onboarding*.js
rm request-cache.js
rm service-worker-validation.js
```

### 4. 清理配置文件
```bash
# 如果不需要环境配置
rm env.config*.js

# 如果不需要选项页面
rm options.html
rm options.js
```

## 📊 清理前后对比

### 清理前
- 文件数量: 50+ 个文件
- 目录结构: 复杂的多层嵌套
- 代码行数: ~3000+ 行
- 维护难度: 高

### 清理后
- 文件数量: 10-15 个文件
- 目录结构: 扁平化结构
- 代码行数: ~800 行
- 维护难度: 低

## ✅ 清理完成检查

清理完成后，确保以下文件存在且功能正常：

### 核心功能文件
- [ ] `manifest.json` - 插件配置正确
- [ ] `background.js` - 后台功能正常
- [ ] `content.js` - 内容提取正常
- [ ] `popup.html/js` - 配置界面正常
- [ ] `sidepanel.html/js` - 侧边栏功能正常
- [ ] `sidepanel.css` - 样式显示正常
- [ ] `icon.png` - 图标显示正常

### 测试文件
- [ ] `test-plugin.html` - 测试页面可用
- [ ] `TESTING_GUIDE.md` - 测试指南完整

### 功能验证
- [ ] 插件可以正常加载
- [ ] 右键菜单正常显示
- [ ] 侧边栏可以打开
- [ ] 保存功能正常工作
- [ ] AI功能基本可用

## 🎯 最终目标

清理完成后，项目应该达到：
1. **简洁性**: 只保留必要的文件
2. **可维护性**: 代码结构清晰
3. **功能完整性**: 核心功能不受影响
4. **性能优化**: 减少不必要的资源加载
5. **易于理解**: 新开发者可以快速上手

## ⚠️ 注意事项

1. **渐进式清理**: 不要一次性删除所有文件，分批进行
2. **功能测试**: 每次删除后都要测试功能是否正常
3. **版本控制**: 使用git记录每次清理的变更
4. **备份重要**: 删除前确保重要文件已备份
5. **文档更新**: 清理后更新相关文档
