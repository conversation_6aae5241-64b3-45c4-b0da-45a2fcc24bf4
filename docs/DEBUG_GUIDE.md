# 硅基流动API调用问题调试指南

## 🔍 问题分析

您遇到的400错误通常是由于请求格式不正确导致的。我已经识别并修复了主要问题：

### 主要问题：模型名称格式错误

**原始错误配置：**
```javascript
models: {
  'qwen2-7b-instruct': 'Qwen2-7B-Instruct',  // ❌ 错误格式
  'qwen2-72b-instruct': 'Qwen2-72B-Instruct', // ❌ 错误格式
}
```

**修复后的正确配置：**
```javascript
models: {
  'Qwen/Qwen3-8B': 'Qwen2.5-7B-Instruct (免费)',  // ✅ 正确格式
  'Qwen/Qwen2.5-72B-Instruct': 'Qwen2.5-72B-Instruct',      // ✅ 正确格式
}
```

## 🛠️ 已修复的文件

1. **`ai-service-manager.js`** - 更新了硅基流动的模型名称格式
2. **`popup.js`** - 更新了设置页面的模型选项
3. **`test-siliconflow-api.html`** - 创建了API测试工具

## 🧪 测试步骤

### 1. 使用API测试工具

1. 打开 `test-siliconflow-api.html` 文件
2. 输入您的硅基流动API密钥
3. 选择模型（推荐使用免费的 `Qwen/Qwen3-8B`）
4. 点击"测试 API"按钮
5. 查看测试结果

### 2. 在扩展中测试

1. 重新加载Chrome扩展
2. 打开扩展设置页面
3. 重新配置AI服务：
   - 选择"硅基流动"
   - 选择模型（如 `Qwen2.5-7B-Instruct (免费)`）
   - 输入API密钥
   - 点击"测试连接"
4. 保存设置
5. 在网页上测试AI功能

## 🔧 常见问题排查

### 问题1：仍然出现400错误

**可能原因：**
- API密钥格式错误
- 请求体参数不正确
- 模型名称仍然使用旧格式

**解决方案：**
1. 检查API密钥是否正确（应该以 `sk-` 开头）
2. 使用测试工具验证API调用
3. 检查浏览器控制台的详细错误信息

### 问题2：401认证错误

**可能原因：**
- API密钥无效或过期
- API密钥权限不足

**解决方案：**
1. 登录硅基流动控制台检查API密钥状态
2. 重新生成API密钥
3. 确认账户余额充足

### 问题3：403权限错误

**可能原因：**
- 选择的模型需要付费但账户余额不足
- API密钥没有访问特定模型的权限

**解决方案：**
1. 使用免费模型进行测试
2. 检查账户余额和权限设置

### 问题4：网络连接问题

**可能原因：**
- 网络防火墙阻止请求
- DNS解析问题
- 代理设置问题

**解决方案：**
1. 检查网络连接
2. 尝试直接访问 https://api.siliconflow.cn
3. 检查代理设置

## 📋 正确的API请求格式

```javascript
{
  "model": "Qwen/Qwen3-8B",  // 使用完整的模型路径
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 1000,
  "stream": false
}
```

## 🔍 调试技巧

### 1. 查看浏览器控制台

1. 按F12打开开发者工具
2. 切换到"Console"标签
3. 查看错误信息和网络请求详情

### 2. 查看网络请求

1. 在开发者工具中切换到"Network"标签
2. 重现问题
3. 查看API请求的详细信息：
   - 请求URL
   - 请求头
   - 请求体
   - 响应状态和内容

### 3. 使用测试工具

使用提供的 `test-siliconflow-api.html` 工具可以：
- 独立测试API调用
- 查看详细的请求和响应信息
- 验证API密钥和模型配置

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. **错误信息**：完整的错误消息
2. **请求详情**：使用的模型名称和API密钥格式
3. **网络请求**：浏览器开发者工具中的网络请求详情
4. **测试结果**：使用测试工具的结果截图

## 🎯 推荐配置

为了获得最佳体验，推荐以下配置：

**免费模型（适合测试）：**
- `Qwen/Qwen3-8B`
- `THUDM/glm-4-9b-chat`
- `internlm/internlm2_5-7b-chat`
- `meta-llama/Meta-Llama-3.1-8B-Instruct`

**付费模型（更好性能）：**
- `Qwen/Qwen2.5-72B-Instruct`
- `deepseek-ai/DeepSeek-V2.5`

## ✅ 验证修复

修复完成后，您应该能够：

1. ✅ 在设置页面成功测试API连接
2. ✅ 在侧边栏中正常使用AI功能
3. ✅ 看到AI处理结果而不是错误信息
4. ✅ 使用测试工具成功调用API

如果所有步骤都正常，说明问题已经解决！
