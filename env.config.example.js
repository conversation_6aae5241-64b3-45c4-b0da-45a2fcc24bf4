// 环境配置文件示例
// 复制此文件为 env.config.js 并填入实际的 API Keys

const EnvConfig = {
  // 环境标识
  environment: 'production', // 'development' | 'production'

  // 默认配置
  defaults: {
    // 默认 AI 供应商
    aiProvider: 'siliconflow',

    // 默认模型
    aiModel: 'Qwen/Qwen3-8B',

    // 是否启用调试日志
    enableDebugLog: false,

    // 请求超时时间（毫秒）
    requestTimeout: 30000,

    // 缓存配置
    enableCache: true,
    cacheExpiry: 300000, // 5分钟

    // 性能配置
    performanceMode: 'balanced', // 'performance' | 'balanced' | 'battery'

    // 通知配置
    showNotifications: true,

    // 自动标签生成
    autoAITags: false
  },

  // AI 供应商配置
  aiProviders: {
    siliconflow: {
      name: '硅基流动',
      baseUrl: 'https://api.siliconflow.cn/v1',
      // 请在这里填入您的硅基流动 API Key
      apiKey: 'sk-your-siliconflow-api-key-here',
      models: {
        'Qwen/Qwen3-8B': 'Qwen2.5-7B-Instruct (免费)',
        'Qwen/Qwen2.5-14B-Instruct': 'Qwen2.5-14B-Instruct',
        'Qwen/Qwen2.5-32B-Instruct': 'Qwen2.5-32B-Instruct',
        'Qwen/Qwen2.5-72B-Instruct': 'Qwen2.5-72B-Instruct',
        'THUDM/glm-4-9b-chat': 'GLM-4-9B-Chat (免费)',
        'internlm/internlm2_5-7b-chat': 'InternLM2.5-7B-Chat (免费)',
        'deepseek-ai/DeepSeek-V2.5': 'DeepSeek-V2.5',
        'meta-llama/Meta-Llama-3.1-8B-Instruct': 'Llama-3.1-8B-Instruct (免费)'
      },
      defaultModel: 'Qwen/Qwen3-8B',
      headers: {
        'Content-Type': 'application/json'
      }
    },

    openrouter: {
      name: 'OpenRouter',
      baseUrl: 'https://openrouter.ai/api/v1',
      // 请在这里填入您的 OpenRouter API Key
      apiKey: 'sk-or-your-openrouter-api-key-here',
      models: {
        'anthropic/claude-3-haiku': 'Claude 3 Haiku',
        'anthropic/claude-3-sonnet': 'Claude 3 Sonnet',
        'openai/gpt-4o-mini': 'GPT-4o Mini',
        'openai/gpt-4o': 'GPT-4o',
        'meta-llama/llama-3.1-8b-instruct': 'Llama 3.1 8B',
        'google/gemini-pro': 'Gemini Pro'
      },
      defaultModel: 'anthropic/claude-3-haiku',
      headers: {
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://chrome-extension-save-to-flomo',
        'X-Title': 'Save to Flomo Extension'
      }
    },

    deepseek: {
      name: 'DeepSeek',
      baseUrl: 'https://api.deepseek.com/v1',
      // 请在这里填入您的 DeepSeek API Key
      apiKey: 'sk-your-deepseek-api-key-here',
      models: {
        'deepseek-chat': 'DeepSeek Chat',
        'deepseek-coder': 'DeepSeek Coder'
      },
      defaultModel: 'deepseek-chat',
      headers: {
        'Content-Type': 'application/json'
      }
    },

    moonshot: {
      name: 'Moonshot AI',
      baseUrl: 'https://api.moonshot.cn/v1',
      // 请在这里填入您的 Moonshot API Key
      apiKey: 'sk-your-moonshot-api-key-here',
      models: {
        'moonshot-v1-8k': 'Moonshot v1 8K',
        'moonshot-v1-32k': 'Moonshot v1 32K',
        'moonshot-v1-128k': 'Moonshot v1 128K'
      },
      defaultModel: 'moonshot-v1-8k',
      headers: {
        'Content-Type': 'application/json'
      }
    }
  },

  // Flomo 配置（如果需要预设）
  flomo: {
    // 可以预设一些常用的 Flomo API 地址格式
    apiUrlTemplate: 'https://flomoapp.com/iwh/{{token}}',
    // 默认留空，让用户配置
    apiUrl: ''
  },

  // 获取当前环境的配置
  getCurrentConfig() {
    const config = {
      ...this.defaults,
      aiProviders: this.aiProviders,
      flomo: this.flomo,
      environment: this.environment
    };

    // 在开发环境中启用调试日志
    if (this.environment === 'development') {
      config.enableDebugLog = true;
    }

    return config;
  },

  // 获取指定供应商的配置
  getProviderConfig(providerId) {
    const provider = this.aiProviders[providerId];
    if (!provider) {
      throw new Error(`未找到供应商配置: ${providerId}`);
    }

    return {
      ...provider,
      id: providerId
    };
  },

  // 获取所有可用的供应商列表
  getAvailableProviders() {
    return Object.keys(this.aiProviders).map(id => ({
      id,
      name: this.aiProviders[id].name,
      models: this.aiProviders[id].models,
      defaultModel: this.aiProviders[id].defaultModel
    }));
  },

  // 验证配置完整性
  validateConfig() {
    const issues = [];

    // 检查默认供应商是否存在
    if (!this.aiProviders[this.defaults.aiProvider]) {
      issues.push(`默认供应商 ${this.defaults.aiProvider} 不存在`);
    }

    // 检查 API Keys
    Object.entries(this.aiProviders).forEach(([id, provider]) => {
      if (!provider.apiKey || provider.apiKey.includes('your-') || provider.apiKey.includes('-here')) {
        issues.push(`供应商 ${id} 的 API Key 未配置`);
      }
    });

    return {
      isValid: issues.length === 0,
      issues
    };
  },

  // 安全地获取 API Key（不在日志中暴露）
  getApiKey(providerId) {
    const provider = this.aiProviders[providerId];
    if (!provider) {
      return null;
    }
    return provider.apiKey;
  },

  // 检查是否为开发环境
  isDevelopment() {
    return this.environment === 'development';
  },

  // 检查是否为生产环境
  isProduction() {
    return this.environment === 'production';
  }
};

// 导出配置
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.EnvConfig = EnvConfig;
} else if (typeof self !== 'undefined') {
  // Service Worker 环境
  self.EnvConfig = EnvConfig;
} else if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  module.exports = EnvConfig;
}

// 在开发环境中输出配置验证结果
if (EnvConfig.isDevelopment()) {
  const validation = EnvConfig.validateConfig();
  if (!validation.isValid) {
    console.warn('⚠️ 环境配置验证失败:', validation.issues);
  } else {
    console.log('✅ 环境配置验证通过');
  }
}

/* 
配置说明：

1. 复制此文件为 env.config.js
2. 将所有 "your-xxx-api-key-here" 替换为实际的 API Keys
3. 根据需要调整默认供应商和模型
4. 在生产环境中设置 environment: 'production'
5. 确保 env.config.js 不被提交到版本控制系统

API Key 获取地址：
- 硅基流动: https://cloud.siliconflow.cn/
- OpenRouter: https://openrouter.ai/
- DeepSeek: https://platform.deepseek.com/
- Moonshot: https://platform.moonshot.cn/

安全提示：
- 请妥善保管您的 API Keys
- 不要在公共场所或不安全的网络环境中配置
- 定期更换 API Keys
- 监控 API 使用情况，防止滥用
*/
