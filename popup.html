<!DOCTYPE html>
<html>

<head>
  <title>Flomo 设置</title>
  <meta charset="UTF-8">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      margin: 0;
      padding: 20px;
      width: 320px;
      background: #f8f9fa;
    }

    .container {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    h1 {
      margin: 0 0 20px 0;
      font-size: 18px;
      color: #333;
      text-align: center;
    }

    .form-group {
      margin-bottom: 16px;
    }

    label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #555;
    }

    input[type="text"] {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }

    input[type="text"]:focus {
      outline: none;
      border-color: #1a73e8;
      box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
    }

    .btn {
      width: 100%;
      padding: 12px;
      background: #1a73e8;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      margin-top: 10px;
    }

    .btn:hover {
      background: #1557b0;
    }

    .status {
      margin-top: 12px;
      padding: 10px;
      border-radius: 4px;
      text-align: center;
      font-size: 13px;
      display: none;
    }

    .status.success {
      background: #e6f4ea;
      color: #137333;
      border: 1px solid #34a853;
    }

    .status.error {
      background: #fce8e6;
      color: #d93025;
      border: 1px solid #ea4335;
    }

    .info {
      background: #e8f0fe;
      border: 1px solid #1a73e8;
      border-radius: 4px;
      padding: 12px;
      margin-top: 16px;
      font-size: 13px;
      color: #1a73e8;
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>Flomo 设置</h1>

    <div class="form-group">
      <label for="api-url">Flomo API 地址</label>
      <input type="text" id="api-url" placeholder="例如: https://flomoapp.com/api/...">
    </div>

    <button class="btn" id="save-button">保存设置</button>

    <div id="status-message" class="status"></div>

    <div class="info">
      💡 AI 功能已预配置，包括智能标签生成和翻译功能，可直接在侧边栏中使用。
    </div>
  </div>

  <script src="popup.js"></script>
</body>

</html>