<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flomo 扩展安全性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #007aff;
            padding-bottom: 10px;
        }

        .test-item {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }

        .test-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .test-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .test-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            font-family: monospace;
            font-size: 12px;
        }

        .test-button {
            background-color: #007aff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        .test-button:hover {
            background-color: #0056b3;
        }

        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }

        .result-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .result-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .summary h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }

        .summary p {
            margin: 0;
            opacity: 0.9;
        }
    </style>
</head>

<body>
    <div class="summary">
        <h1>🔒 Flomo 扩展安全性测试</h1>
        <p>验证输入验证、API密钥保护、XSS防护等安全措施的有效性</p>
    </div>

    <div class="test-container">
        <div class="test-title">🔑 API密钥泄露测试</div>

        <div class="test-item">
            <div class="test-name">1. 控制台日志泄露检测</div>
            <div class="test-description">检查控制台是否会输出API密钥信息</div>
            <input type="text" class="test-input" id="api-key-input" placeholder="输入测试API密钥"
                value="sk-test123456789abcdef">
            <button class="test-button" id="test-api-key-btn">测试日志泄露</button>
            <div id="api-key-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-item">
            <div class="test-name">2. 网络请求泄露检测</div>
            <div class="test-description">检查网络请求中是否会暴露完整API密钥</div>
            <button class="test-button" id="test-network-btn">测试网络泄露</button>
            <div id="network-result" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-title">🛡️ 输入验证测试</div>

        <div class="test-item">
            <div class="test-name">3. XSS攻击防护</div>
            <div class="test-description">测试输入验证器是否能防止XSS攻击</div>
            <textarea class="test-input" id="xss-input" rows="3" placeholder="输入XSS测试代码">
&lt;script&gt;alert('XSS攻击')&lt;/script&gt;
&lt;img src="x" onerror="alert('XSS')"&gt;
&lt;iframe src="javascript:alert('XSS')"&gt;&lt;/iframe&gt;</textarea>
            <button class="test-button" id="test-xss-btn">测试XSS防护</button>
            <div id="xss-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-item">
            <div class="test-name">4. URL验证测试</div>
            <div class="test-description">测试URL验证器是否能阻止恶意URL</div>
            <textarea class="test-input" id="url-input" rows="4" placeholder="输入测试URL">
http://malicious-site.com/api
https://evil.com/steal-data
javascript:alert('evil')
ftp://unsafe-protocol.com</textarea>
            <button class="test-button" id="test-url-btn">测试URL验证</button>
            <div id="url-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-item">
            <div class="test-name">5. 内容长度限制测试</div>
            <div class="test-description">测试内容长度限制是否有效</div>
            <button class="test-button" id="test-length-btn">生成超长内容测试</button>
            <div id="length-result" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-title">🔐 存储安全测试</div>

        <div class="test-item">
            <div class="test-name">6. 敏感数据存储检测</div>
            <div class="test-description">检查本地存储中是否有明文敏感信息</div>
            <button class="test-button" id="test-storage-btn">检查存储安全</button>
            <div id="storage-result" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <div class="test-container">
        <div class="test-title">📊 测试结果汇总</div>
        <div id="test-summary" class="test-result result-info">
            点击上方测试按钮开始安全性测试...
        </div>
    </div>

    <script src="input-validator.js"></script>
    <script src="security-test.js"></script>
</body>

</html>