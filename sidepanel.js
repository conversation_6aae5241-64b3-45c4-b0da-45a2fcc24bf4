// 简化的侧边栏脚本
document.addEventListener('DOMContentLoaded', async () => {
  const contentText = document.getElementById('content-text');
  const contentInfo = document.getElementById('content-info');
  const pageTitle = document.getElementById('page-title');
  const pageUrl = document.getElementById('page-url');
  const statusMessage = document.getElementById('status-message');
  const saveButton = document.getElementById('save-to-flomo');
  const generateTagsBtn = document.getElementById('generate-tags');
  const translateBtn = document.getElementById('translate-text');

  // 显示状态消息
  function showStatus(message, type) {
    statusMessage.textContent = message;
    statusMessage.className = `status ${type}`;
    statusMessage.style.display = 'block';

    if (type === 'success') {
      setTimeout(() => {
        statusMessage.style.display = 'none';
      }, 3000);
    }
  }

  // 加载待处理的内容
  async function loadPendingContent() {
    try {
      const result = await chrome.storage.local.get('pendingContent');
      if (result.pendingContent) {
        const { selectedText, pageTitle: title, pageUrl: url } = result.pendingContent;

        if (selectedText) {
          contentText.value = selectedText;

          if (title && url) {
            pageTitle.textContent = title;
            pageUrl.textContent = url;
            contentInfo.style.display = 'block';
          }
        }

        // 清理临时数据
        await chrome.storage.local.remove('pendingContent');
      }
    } catch (error) {
      console.error('加载内容失败:', error);
    }
  }

  // 保存到Flomo
  async function saveToFlomo() {
    const content = contentText.value.trim();

    if (!content) {
      showStatus('请输入要保存的内容', 'error');
      return;
    }

    saveButton.disabled = true;
    saveButton.classList.add('loading');
    saveButton.textContent = '保存中...';

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'saveToFlomo',
        content: content
      });

      if (response.success) {
        showStatus('内容已成功保存到 Flomo！', 'success');
        contentText.value = '';
        contentInfo.style.display = 'none';
      } else {
        showStatus(response.error || '保存失败', 'error');
      }
    } catch (error) {
      showStatus('保存失败: ' + error.message, 'error');
    } finally {
      saveButton.disabled = false;
      saveButton.classList.remove('loading');
      saveButton.textContent = '保存到 Flomo';
    }
  }

  // 简单的AI功能 - 生成标签
  async function generateTags() {
    const content = contentText.value.trim();

    if (!content) {
      showStatus('请先输入内容', 'error');
      return;
    }

    generateTagsBtn.disabled = true;
    generateTagsBtn.classList.add('loading');

    try {
      // 简单的标签生成逻辑
      const tags = await simpleTagGeneration(content);
      contentText.value = content + '\n\n' + tags;
      showStatus('标签已生成', 'success');
    } catch (error) {
      showStatus('标签生成失败', 'error');
    } finally {
      generateTagsBtn.disabled = false;
      generateTagsBtn.classList.remove('loading');
    }
  }

  // 简单的翻译功能
  async function translateText() {
    const content = contentText.value.trim();

    if (!content) {
      showStatus('请先输入内容', 'error');
      return;
    }

    translateBtn.disabled = true;
    translateBtn.classList.add('loading');

    try {
      // 检测语言并翻译
      const isChineseText = /[\u4e00-\u9fff]/.test(content);
      const translatedText = await simpleTranslation(content, isChineseText);
      contentText.value = content + '\n\n---\n\n' + translatedText;
      showStatus('翻译已完成', 'success');
    } catch (error) {
      showStatus('翻译失败', 'error');
    } finally {
      translateBtn.disabled = false;
      translateBtn.classList.remove('loading');
    }
  }

  // 智能标签生成（改进的关键词提取 + 语义分析）
  async function simpleTagGeneration(text) {
    try {
      // 使用改进的关键词提取
      const keywords = extractSmartKeywords(text);

      // 生成语义相关的标签
      const semanticTags = generateSemanticTags(text);

      // 合并并去重
      const allTags = [...new Set([...keywords, ...semanticTags])];

      return '#' + allTags.slice(0, 6).join(' #');
    } catch (error) {
      console.error('标签生成失败:', error);
      // 降级到简单提取
      const keywords = extractKeywords(text);
      return '#' + keywords.slice(0, 5).join(' #');
    }
  }

  // 真实翻译功能（使用MyMemory免费API）
  async function simpleTranslation(text, isChinese) {
    try {
      const sourceLang = isChinese ? 'zh' : 'en';
      const targetLang = isChinese ? 'en' : 'zh';

      // 使用MyMemory翻译API
      const response = await fetch(`https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=${sourceLang}|${targetLang}`);

      if (!response.ok) {
        throw new Error('翻译服务暂时不可用');
      }

      const data = await response.json();

      if (data.responseStatus === 200 && data.responseData) {
        return data.responseData.translatedText;
      } else {
        throw new Error('翻译失败');
      }
    } catch (error) {
      console.error('翻译失败:', error);
      // 降级到占位符
      return isChinese ? '[Translation temporarily unavailable]' : '[翻译服务暂时不可用]';
    }
  }

  // 改进的智能关键词提取
  function extractSmartKeywords(text) {
    // 中英文停用词
    const stopWords = new Set([
      // 英文停用词
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their',
      // 中文停用词
      '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '里', '就是', '还', '把', '比', '让', '如果', '因为', '所以', '但是', '然后', '可以', '已经', '现在', '什么', '怎么', '为什么', '哪里', '谁', '多少', '怎样'
    ]);

    // 分词和清理
    const words = text.toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 1 && !stopWords.has(word));

    // 计算词频和位置权重
    const wordStats = {};
    words.forEach((word, index) => {
      if (!wordStats[word]) {
        wordStats[word] = { count: 0, positions: [] };
      }
      wordStats[word].count++;
      wordStats[word].positions.push(index);
    });

    // 计算TF-IDF风格的分数
    const totalWords = words.length;
    const scores = {};

    Object.keys(wordStats).forEach(word => {
      const tf = wordStats[word].count / totalWords;
      const positionBonus = wordStats[word].positions.some(pos => pos < totalWords * 0.2) ? 1.5 : 1; // 前20%位置加权
      scores[word] = tf * positionBonus * (word.length > 3 ? 1.2 : 1); // 长词加权
    });

    // 按分数排序并返回
    return Object.keys(scores)
      .sort((a, b) => scores[b] - scores[a])
      .slice(0, 5);
  }

  // 生成语义相关标签
  function generateSemanticTags(text) {
    const semanticRules = [
      // 技术相关
      { pattern: /(技术|科技|开发|编程|代码|算法|数据|AI|人工智能|机器学习|深度学习)/i, tags: ['技术', '科技'] },
      { pattern: /(technology|development|programming|code|algorithm|data|artificial|intelligence|machine|learning)/i, tags: ['technology', 'development'] },

      // 商业相关
      { pattern: /(商业|业务|市场|营销|销售|管理|企业|公司)/i, tags: ['商业', '管理'] },
      { pattern: /(business|market|marketing|sales|management|company|enterprise)/i, tags: ['business', 'management'] },

      // 教育相关
      { pattern: /(教育|学习|知识|研究|学术|培训)/i, tags: ['教育', '学习'] },
      { pattern: /(education|learning|knowledge|research|academic|training)/i, tags: ['education', 'learning'] },

      // 生活相关
      { pattern: /(生活|健康|旅行|美食|娱乐|运动)/i, tags: ['生活', '日常'] },
      { pattern: /(life|health|travel|food|entertainment|sports)/i, tags: ['life', 'lifestyle'] }
    ];

    const tags = [];
    semanticRules.forEach(rule => {
      if (rule.pattern.test(text)) {
        tags.push(...rule.tags);
      }
    });

    return [...new Set(tags)]; // 去重
  }

  // 简单的关键词提取（备用）
  function extractKeywords(text) {
    // 移除标点符号，分词
    const words = text.replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 1);

    // 简单的词频统计
    const wordCount = {};
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    // 按频率排序
    return Object.keys(wordCount)
      .sort((a, b) => wordCount[b] - wordCount[a])
      .slice(0, 5);
  }

  // 事件监听
  saveButton.addEventListener('click', saveToFlomo);
  generateTagsBtn.addEventListener('click', generateTags);
  translateBtn.addEventListener('click', translateText);

  // 初始化
  await loadPendingContent();
});
