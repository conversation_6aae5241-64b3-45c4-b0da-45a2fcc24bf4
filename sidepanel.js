// 简化的侧边栏脚本
document.addEventListener('DOMContentLoaded', async () => {
  const contentText = document.getElementById('content-text');
  const contentInfo = document.getElementById('content-info');
  const pageTitle = document.getElementById('page-title');
  const pageUrl = document.getElementById('page-url');
  const statusMessage = document.getElementById('status-message');
  const saveButton = document.getElementById('save-to-flomo');
  const generateTagsBtn = document.getElementById('generate-tags');
  const translateBtn = document.getElementById('translate-text');

  // 显示状态消息
  function showStatus(message, type) {
    statusMessage.textContent = message;
    statusMessage.className = `status ${type}`;
    statusMessage.style.display = 'block';

    if (type === 'success') {
      setTimeout(() => {
        statusMessage.style.display = 'none';
      }, 3000);
    }
  }

  // 加载待处理的内容
  async function loadPendingContent() {
    try {
      const result = await chrome.storage.local.get('pendingContent');
      if (result.pendingContent) {
        const { selectedText, pageTitle: title, pageUrl: url } = result.pendingContent;

        if (selectedText) {
          contentText.value = selectedText;

          if (title && url) {
            pageTitle.textContent = title;
            pageUrl.textContent = url;
            contentInfo.style.display = 'block';
          }
        }

        // 清理临时数据
        await chrome.storage.local.remove('pendingContent');
      }
    } catch (error) {
      console.error('加载内容失败:', error);
    }
  }

  // 保存到Flomo
  async function saveToFlomo() {
    const content = contentText.value.trim();

    if (!content) {
      showStatus('请输入要保存的内容', 'error');
      return;
    }

    saveButton.disabled = true;
    saveButton.classList.add('loading');
    saveButton.textContent = '保存中...';

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'saveToFlomo',
        content: content
      });

      if (response.success) {
        showStatus('内容已成功保存到 Flomo！', 'success');
        contentText.value = '';
        contentInfo.style.display = 'none';
      } else {
        showStatus(response.error || '保存失败', 'error');
      }
    } catch (error) {
      showStatus('保存失败: ' + error.message, 'error');
    } finally {
      saveButton.disabled = false;
      saveButton.classList.remove('loading');
      saveButton.textContent = '保存到 Flomo';
    }
  }

  // 简单的AI功能 - 生成标签
  async function generateTags() {
    const content = contentText.value.trim();

    if (!content) {
      showStatus('请先输入内容', 'error');
      return;
    }

    generateTagsBtn.disabled = true;
    generateTagsBtn.classList.add('loading');

    try {
      // 简单的标签生成逻辑
      const tags = await simpleTagGeneration(content);
      contentText.value = content + '\n\n' + tags;
      showStatus('标签已生成', 'success');
    } catch (error) {
      showStatus('标签生成失败', 'error');
    } finally {
      generateTagsBtn.disabled = false;
      generateTagsBtn.classList.remove('loading');
    }
  }

  // 简单的翻译功能
  async function translateText() {
    const content = contentText.value.trim();

    if (!content) {
      showStatus('请先输入内容', 'error');
      return;
    }

    translateBtn.disabled = true;
    translateBtn.classList.add('loading');

    try {
      // 检测语言并翻译
      const isChineseText = /[\u4e00-\u9fff]/.test(content);
      const translatedText = await simpleTranslation(content, isChineseText);
      contentText.value = content + '\n\n---\n\n' + translatedText;
      showStatus('翻译已完成', 'success');
    } catch (error) {
      showStatus('翻译失败', 'error');
    } finally {
      translateBtn.disabled = false;
      translateBtn.classList.remove('loading');
    }
  }

  // 简单的标签生成（基于关键词提取）
  async function simpleTagGeneration(text) {
    // 简单的关键词提取逻辑
    const keywords = extractKeywords(text);
    return '#' + keywords.slice(0, 5).join(' #');
  }

  // 简单的翻译功能（使用免费API或本地逻辑）
  async function simpleTranslation(text, isChinese) {
    // 这里可以集成简单的翻译API
    // 暂时返回占位符
    return isChinese ? '[English Translation]' : '[中文翻译]';
  }

  // 简单的关键词提取
  function extractKeywords(text) {
    // 移除标点符号，分词
    const words = text.replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 1);

    // 简单的词频统计
    const wordCount = {};
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    // 按频率排序
    return Object.keys(wordCount)
      .sort((a, b) => wordCount[b] - wordCount[a])
      .slice(0, 5);
  }

  // 事件监听
  saveButton.addEventListener('click', saveToFlomo);
  generateTagsBtn.addEventListener('click', generateTags);
  translateBtn.addEventListener('click', translateText);

  // 初始化
  await loadPendingContent();
});
