<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple AI Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }

    button {
      padding: 10px 20px;
      margin: 10px;
      background-color: #007cba;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    button:disabled {
      background-color: #ccc;
    }

    .result {
      margin: 10px 0;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #f9f9f9;
    }

    .error {
      background-color: #ffe6e6;
      border-color: #ff9999;
      color: #cc0000;
    }

    .success {
      background-color: #e6ffe6;
      border-color: #99ff99;
      color: #006600;
    }
  </style>
</head>

<body>
  <h1>Simple AI Test</h1>

  <button id="testBtn">测试AI配置</button>
  <div id="results"></div>

  <!-- 加载依赖文件 -->
  <script src="env.config.js"></script>
  <script src="ai-service-manager.js"></script>
  <script src="ai-functions.js"></script>

  <script>
    // 模拟Chrome存储API
    if (typeof chrome === 'undefined') {
      window.chrome = {
        storage: {
          sync: {
            get: function (keys) {
              return Promise.resolve({
                aiProvider: 'siliconflow',
                aiModel: 'Qwen/Qwen3-8B',
                aiApiKeys: {
                  siliconflow: 'sk-test-key-for-debugging'
                }
              });
            }
          }
        }
      };
    }

    let testAiServiceManager, testAiFunctions;

    // 初始化
    document.addEventListener('DOMContentLoaded', function () {
      try {
        testAiServiceManager = new AIServiceManager();
        testAiFunctions = new AIFunctions(testAiServiceManager);

        // 绑定事件
        document.getElementById('testBtn').addEventListener('click', testAIConfig);

        addResult('✅ 初始化成功', 'success');
      } catch (error) {
        addResult('❌ 初始化失败: ' + error.message, 'error');
      }
    });

    // 测试AI配置
    async function testAIConfig() {
      const button = document.getElementById('testBtn');
      button.disabled = true;
      button.textContent = '测试中...';

      try {
        // 测试获取配置
        const config = await testAiFunctions.getAIConfig();
        addResult(`✅ 配置获取成功: provider=${config.provider}, model=${config.model}`, 'success');

        // 测试模型字段是否存在
        if (config.model) {
          addResult(`✅ 模型字段正确: ${config.model}`, 'success');
        } else {
          addResult('❌ 模型字段为空!', 'error');
        }

        // 测试API密钥
        if (config.apiKey) {
          addResult(`✅ API密钥存在，长度: ${config.apiKey.length}`, 'success');
        } else {
          addResult('❌ API密钥缺失!', 'error');
        }

      } catch (error) {
        addResult('❌ 配置测试失败: ' + error.message, 'error');
        console.error('配置测试错误:', error);
      } finally {
        button.disabled = false;
        button.textContent = '测试AI配置';
      }
    }

    // 添加结果
    function addResult(message, type = 'result') {
      const resultsDiv = document.getElementById('results');
      const resultDiv = document.createElement('div');
      resultDiv.className = `result ${type}`;
      resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      resultsDiv.appendChild(resultDiv);
    }
  </script>
</body>

</html>