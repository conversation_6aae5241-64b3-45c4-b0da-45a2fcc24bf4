// AI功能模块单元测试
describe('AIFunctions', () => {
  let aiFunctions;
  let mockAIServiceManager;
  let originalChrome;

  beforeEach(() => {
    // 模拟Chrome API
    originalChrome = global.chrome;
    global.chrome = {
      storage: {
        sync: {
          get: testFramework.createSpy(() => Promise.resolve({
            aiProvider: 'siliconflow',
            aiApiKeys: {
              siliconflow: 'test-api-key'
            },
            aiModels: {
              siliconflow: 'Qwen/Qwen3-8B'
            }
          }))
        }
      },
      runtime: {
        sendMessage: testFramework.createSpy(() => Promise.resolve({
          success: true,
          providers: [{
            id: 'siliconflow',
            name: '硅基流动',
            models: {
              'Qwen/Qwen3-8B': 'Qwen2.5-7B-Instruct (免费)'
            }
          }]
        }))
      }
    };

    // 创建AI功能实例
    aiFunctions = new AIFunctions();
  });

  afterEach(() => {
    global.chrome = originalChrome;
  });

  describe('配置获取', () => {
    it('应该正确获取AI配置', async () => {
      const config = await aiFunctions.getAIConfig();

      expect(config.provider).toBe('siliconflow');
      expect(config.apiKey).toBe('test-api-key');
      expect(config.model).toBe('Qwen/Qwen3-8B');
    });

    it('应该处理缺失的配置', async () => {
      global.chrome.storage.sync.get = testFramework.createSpy(() => Promise.resolve({}));

      try {
        await aiFunctions.getAIConfig();
        expect(true).toBe(false); // 不应该到达这里
      } catch (error) {
        expect(error.message).toContain('AI服务未配置');
      }
    });

    it('应该处理缺失的API密钥', async () => {
      global.chrome.storage.sync.get = testFramework.createSpy(() => Promise.resolve({
        aiProvider: 'siliconflow'
      }));

      try {
        await aiFunctions.getAIConfig();
        expect(true).toBe(false); // 不应该到达这里
      } catch (error) {
        expect(error.message).toContain('API密钥未配置');
      }
    });
  });

  describe('标签生成功能', () => {
    let originalFetch;

    beforeEach(() => {
      originalFetch = global.fetch;
    });

    afterEach(() => {
      global.fetch = originalFetch;
    });

    it('应该生成相关标签', async () => {
      global.fetch = testFramework.createSpy(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '#技术 #编程 #JavaScript'
            }
          }]
        })
      }));

      const tags = await aiFunctions.generateTags('这是一篇关于JavaScript编程的文章');

      expect(tags).toContain('#技术');
      expect(tags).toContain('#编程');
      expect(tags).toContain('#JavaScript');
    });

    it('应该处理空内容', async () => {
      try {
        await aiFunctions.generateTags('');
        expect(true).toBe(false); // 不应该到达这里
      } catch (error) {
        expect(error.message).toContain('内容不能为空');
      }
    });

    it('应该处理API错误', async () => {
      global.fetch = testFramework.createSpy(() => Promise.resolve({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      }));

      try {
        await aiFunctions.generateTags('测试内容');
        expect(true).toBe(false); // 不应该到达这里
      } catch (error) {
        expect(error.message).toContain('401');
      }
    });
  });

  describe('摘要生成功能', () => {
    let originalFetch;

    beforeEach(() => {
      originalFetch = global.fetch;
    });

    afterEach(() => {
      global.fetch = originalFetch;
    });

    it('应该生成内容摘要', async () => {
      global.fetch = testFramework.createSpy(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '这是一篇关于JavaScript编程技术的文章摘要。'
            }
          }]
        })
      }));

      const summary = await aiFunctions.generateSummary('这是一篇很长的关于JavaScript编程技术的详细文章内容...');

      expect(summary).toContain('JavaScript');
      expect(summary).toContain('编程');
      expect(summary.length).toBeLessThan(200); // 摘要应该比原文短
    });

    it('应该处理短文本', async () => {
      const shortText = '短文本';

      try {
        await aiFunctions.generateSummary(shortText);
        expect(true).toBe(false); // 不应该到达这里
      } catch (error) {
        expect(error.message).toContain('内容太短');
      }
    });
  });

  describe('格式整理功能', () => {
    let originalFetch;

    beforeEach(() => {
      originalFetch = global.fetch;
    });

    afterEach(() => {
      global.fetch = originalFetch;
    });

    it('应该整理文本格式', async () => {
      global.fetch = testFramework.createSpy(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '# 标题\n\n这是整理后的内容，格式清晰，结构合理。\n\n## 要点\n- 要点1\n- 要点2'
            }
          }]
        })
      }));

      const formatted = await aiFunctions.formatContent('标题这是混乱的内容格式不清晰要点1要点2');

      expect(formatted).toContain('#');
      expect(formatted).toContain('-');
      expect(formatted.length).toBeGreaterThan(0);
    });
  });

  describe('翻译功能', () => {
    let originalFetch;

    beforeEach(() => {
      originalFetch = global.fetch;
    });

    afterEach(() => {
      global.fetch = originalFetch;
    });

    it('应该翻译中文到英文', async () => {
      global.fetch = testFramework.createSpy(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '**中文：** 你好世界\n**English:** Hello World'
            }
          }]
        })
      }));

      const translation = await aiFunctions.translateContent('你好世界');

      expect(translation).toContain('你好世界');
      expect(translation).toContain('Hello World');
      expect(translation).toContain('**中文：**');
      expect(translation).toContain('**English:**');
    });

    it('应该翻译英文到中文', async () => {
      global.fetch = testFramework.createSpy(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '**English:** Hello World\n**中文：** 你好世界'
            }
          }]
        })
      }));

      const translation = await aiFunctions.translateContent('Hello World');

      expect(translation).toContain('Hello World');
      expect(translation).toContain('你好世界');
    });
  });

  describe('通用AI请求', () => {
    let originalFetch;

    beforeEach(() => {
      originalFetch = global.fetch;
    });

    afterEach(() => {
      global.fetch = originalFetch;
    });

    it('应该发送正确的请求格式', async () => {
      global.fetch = testFramework.createSpy(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: { content: 'AI响应' }
          }]
        })
      }));

      await aiFunctions.makeAIRequest('测试提示词');

      expect(global.fetch._calls).toHaveLength(1);
      const [url, options] = global.fetch._calls[0];

      expect(url).toContain('api.siliconflow.cn');
      expect(options.method).toBe('POST');
      expect(options.headers).toHaveProperty('Authorization');
      expect(options.headers).toHaveProperty('Content-Type', 'application/json');

      const body = JSON.parse(options.body);
      expect(body.model).toBe('Qwen/Qwen3-8B');
      expect(body.messages).toHaveLength(1);
      expect(body.messages[0].content).toBe('测试提示词');
    });

    it('应该处理网络错误', async () => {
      global.fetch = testFramework.createSpy(() => Promise.reject(new Error('Network error')));

      try {
        await aiFunctions.makeAIRequest('测试提示词');
        expect(true).toBe(false); // 不应该到达这里
      } catch (error) {
        expect(error.message).toContain('Network error');
      }
    });

    it('应该处理超时', async () => {
      global.fetch = testFramework.createSpy(() => new Promise(() => { })); // 永不resolve

      try {
        await aiFunctions.makeAIRequest('测试提示词');
        expect(true).toBe(false); // 不应该到达这里
      } catch (error) {
        expect(error.message).toContain('超时');
      }
    });
  });

  describe('输入验证', () => {
    it('应该验证内容长度', () => {
      const longContent = 'a'.repeat(20000);

      expect(() => {
        aiFunctions.validateInput(longContent);
      }).toThrow('内容过长');
    });

    it('应该验证内容不为空', () => {
      expect(() => {
        aiFunctions.validateInput('');
      }).toThrow('内容不能为空');

      expect(() => {
        aiFunctions.validateInput('   ');
      }).toThrow('内容不能为空');
    });

    it('应该通过有效内容验证', () => {
      expect(() => {
        aiFunctions.validateInput('有效的内容');
      }).not.toThrow();
    });
  });
});
